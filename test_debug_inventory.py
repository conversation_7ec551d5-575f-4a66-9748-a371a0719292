#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشخيص مشكلة أزرار المخزون
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer
from database import get_session
from ui.inventory import InventoryWidget

class DebugWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تشخيص أزرار المخزون")
        self.setGeometry(100, 100, 1000, 700)
        
        # إنشاء جلسة قاعدة البيانات
        self.session = get_session()
        
        # إنشاء واجهة المخزون
        self.inventory_widget = InventoryWidget(self.session)
        self.setCentralWidget(self.inventory_widget)
        
        print("✅ تم إنشاء نافذة التشخيص")
        
        # اختبار بعد 2 ثانية
        QTimer.singleShot(2000, self.debug_buttons)
        
        # اختبار الدالة مباشرة بعد 4 ثوان
        QTimer.singleShot(4000, self.test_function_directly)
    
    def debug_buttons(self):
        """تشخيص حالة الأزرار"""
        print("\n🔍 تشخيص حالة الأزرار:")
        
        # فحص وجود الدالة
        if hasattr(self.inventory_widget, 'simple_button_update'):
            print("✅ الدالة simple_button_update موجودة")
        else:
            print("❌ الدالة simple_button_update غير موجودة")
            return
        
        # فحص وجود الجدول
        if hasattr(self.inventory_widget, 'inventory_table'):
            print("✅ الجدول inventory_table موجود")
            
            # فحص الربط
            try:
                # محاولة فصل الربط وإعادة ربطه
                self.inventory_widget.inventory_table.itemSelectionChanged.disconnect()
                print("✅ تم فصل الربط السابق")
            except:
                print("⚠️ لا يوجد ربط سابق")
            
            # إعادة الربط
            self.inventory_widget.inventory_table.itemSelectionChanged.connect(
                self.inventory_widget.simple_button_update
            )
            print("✅ تم إعادة ربط الدالة")
            
        else:
            print("❌ الجدول inventory_table غير موجود")
        
        # فحص حالة الأزرار الحالية
        buttons = [
            ('edit_button', 'تعديل'),
            ('delete_button', 'حذف'),
            ('view_button', 'عرض'),
            ('adjust_button', 'تعديل كمية')
        ]
        
        for button_name, desc in buttons:
            if hasattr(self.inventory_widget, button_name):
                button = getattr(self.inventory_widget, button_name)
                enabled = button.isEnabled()
                print(f"🔍 {desc}: {'✅ مفعل' if enabled else '❌ معطل'}")
            else:
                print(f"❌ الزر {desc} غير موجود")
    
    def test_function_directly(self):
        """اختبار الدالة مباشرة"""
        print("\n🧪 اختبار الدالة مباشرة:")
        
        if hasattr(self.inventory_widget, 'simple_button_update'):
            try:
                print("🔥 استدعاء الدالة مباشرة...")
                self.inventory_widget.simple_button_update()
                print("✅ تم استدعاء الدالة بنجاح")
            except Exception as e:
                print(f"❌ خطأ في استدعاء الدالة: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ الدالة غير موجودة")

def main():
    app = QApplication(sys.argv)
    
    try:
        window = DebugWindow()
        window.show()
        
        print("🚀 تم تشغيل تشخيص أزرار المخزون")
        print("📋 سيتم التشخيص تلقائياً...")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
