#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار خاصية إغلاق الأزرار في قسم المخزون - محسن
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import QTimer
from database import get_session
from ui.inventory import InventoryWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار أزرار المخزون - محسن")
        self.setGeometry(100, 100, 1200, 900)

        # إنشاء جلسة قاعدة البيانات
        self.session = get_session()

        # إنشاء الواجهة الرئيسية
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # إضافة أزرار الاختبار
        test_buttons_layout = QHBoxLayout()

        self.test_button1 = QPushButton("🔧 إجبار تحديث حالة الأزرار")
        self.test_button1.clicked.connect(self.force_update_buttons)
        test_buttons_layout.addWidget(self.test_button1)

        self.test_button2 = QPushButton("🔄 إعادة تعيين الأزرار")
        self.test_button2.clicked.connect(self.reset_buttons)
        test_buttons_layout.addWidget(self.test_button2)

        main_layout.addLayout(test_buttons_layout)

        # إنشاء واجهة المخزون
        self.inventory_widget = InventoryWidget(self.session)
        main_layout.addWidget(self.inventory_widget)

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        print("✅ تم إنشاء نافذة اختبار المخزون المحسنة")
        print("🔍 اختبر النقر على الجدول لرؤية تغيير حالة الأزرار")

        # تأجيل اختبار الأزرار
        QTimer.singleShot(2000, self.test_buttons_after_load)

    def force_update_buttons(self):
        """إجبار تحديث حالة الأزرار"""
        print("🔧 المستخدم طلب إجبار تحديث حالة الأزرار")
        if hasattr(self.inventory_widget, 'force_button_state_update'):
            self.inventory_widget.force_button_state_update()
        else:
            print("❌ دالة force_button_state_update غير موجودة")

    def reset_buttons(self):
        """إعادة تعيين الأزرار"""
        print("🔄 إعادة تعيين حالة الأزرار")
        if hasattr(self.inventory_widget, 'initialize_button_states'):
            self.inventory_widget.user_interacted_with_table = False
            self.inventory_widget.initialize_button_states()
        else:
            print("❌ دالة initialize_button_states غير موجودة")

    def test_buttons_after_load(self):
        """اختبار الأزرار بعد تحميل البيانات"""
        print("🧪 بدء اختبار الأزرار بعد تحميل البيانات...")

        # اختبار حالة الأزرار الحالية
        buttons_to_test = [
            ('add_button', 'إضافة'),
            ('edit_button', 'تعديل'),
            ('delete_button', 'حذف'),
            ('view_button', 'عرض التفاصيل'),
            ('adjust_button', 'تعديل الكمية'),
            ('refresh_button', 'تحديث'),
            ('export_button', 'تصدير'),
            ('statistics_button', 'إحصائيات'),
            ('columns_visibility_button', 'إدارة الأعمدة')
        ]

        for button_name, button_desc in buttons_to_test:
            if hasattr(self.inventory_widget, button_name):
                button = getattr(self.inventory_widget, button_name)
                enabled = button.isEnabled()
                print(f"🔍 الزر {button_desc}: {'مفعل' if enabled else 'معطل'}")
            else:
                print(f"❌ الزر {button_desc} غير موجود")

def test_inventory_buttons():
    """اختبار خاصية إغلاق الأزرار في المخزون"""
    app = QApplication(sys.argv)

    try:
        # إنشاء النافذة
        window = TestWindow()
        window.show()

        print("🚀 تم تشغيل اختبار أزرار المخزون المحسن")
        print("📋 تعليمات الاختبار:")
        print("   1. انقر على 'إجبار تحديث حالة الأزرار' لاختبار الخاصية")
        print("   2. انقر على أي صف في الجدول")
        print("   3. لاحظ تغيير حالة الأزرار")
        print("   4. انقر على 'إعادة تعيين الأزرار' لإعادة تفعيل جميع الأزرار")

        # تشغيل التطبيق
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار المخزون: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_inventory_buttons()
