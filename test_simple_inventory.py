#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لخاصية إغلاق الأزرار في المخزون
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer
from database import get_session
from ui.inventory import InventoryWidget

class SimpleTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار بسيط - أزرار المخزون")
        self.setGeometry(100, 100, 1000, 700)
        
        # إنشاء جلسة قاعدة البيانات
        self.session = get_session()
        
        # إنشاء واجهة المخزون
        self.inventory_widget = InventoryWidget(self.session)
        self.setCentralWidget(self.inventory_widget)
        
        print("✅ تم إنشاء نافذة اختبار بسيطة")
        
        # اختبار الأزرار بعد 3 ثوان
        QTimer.singleShot(3000, self.test_buttons)
    
    def test_buttons(self):
        """اختبار حالة الأزرار"""
        print("\n🧪 اختبار حالة الأزرار:")
        
        buttons = [
            ('add_button', 'إضافة'),
            ('edit_button', 'تعديل'),
            ('delete_button', 'حذف'),
            ('view_button', 'عرض التفاصيل'),
            ('adjust_button', 'تعديل الكمية'),
            ('refresh_button', 'تحديث'),
            ('export_button', 'تصدير'),
            ('statistics_button', 'إحصائيات'),
            ('columns_visibility_button', 'إدارة الأعمدة')
        ]
        
        for button_name, button_desc in buttons:
            if hasattr(self.inventory_widget, button_name):
                button = getattr(self.inventory_widget, button_name)
                enabled = button.isEnabled()
                print(f"🔍 {button_desc}: {'✅ مفعل' if enabled else '❌ معطل'}")
            else:
                print(f"❌ الزر {button_desc} غير موجود")
        
        print("\n🔧 محاولة إجبار تحديث حالة الأزرار...")
        if hasattr(self.inventory_widget, 'update_button_states'):
            self.inventory_widget.update_button_states()
        
        print("\n🔧 اختبار بعد التحديث:")
        for button_name, button_desc in buttons:
            if hasattr(self.inventory_widget, button_name):
                button = getattr(self.inventory_widget, button_name)
                enabled = button.isEnabled()
                print(f"🔍 {button_desc}: {'✅ مفعل' if enabled else '❌ معطل'}")

def main():
    app = QApplication(sys.argv)
    
    try:
        window = SimpleTestWindow()
        window.show()
        
        print("🚀 تم تشغيل الاختبار البسيط")
        print("📋 سيتم اختبار الأزرار تلقائياً بعد 3 ثوان")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
